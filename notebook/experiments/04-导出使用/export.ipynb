#%%
import pandas as pd
import sys
import os
import json
import cv2
import uuid
sys.path.append('../../../src')
#%%
from sr_od.utils import os_utils
from sr_od.devtools import dataset_utils
from sr_od.devtools import label_studio_utils
from sr_od.devtools import ultralytics_utils
#%%
ultralytics_utils.init_ultralytics_settings()
#%%
from ultralytics import YOLO
#%%
model = YOLO(ultralytics_utils.get_train_model_path('full-test-v0', 'train', 'best'))
#%%
model.export(format='onnx', imgsz=(384, 640))
#%%
from sr_od.detector import YOLOv8
#%%
onnx_path = 'F:\\code\\workspace\\StarRail-YOLO\\ultralytics\\runs\\xuzu-test\\train\\weights\\best.onnx'
#%%
model = YOLOv8(onnx_path, cuda=False)
#%%
import matplotlib.pyplot as plt
%matplotlib inline
import cv2
#%%
dataset_name = 'xuzu-test'
img_dir = dataset_utils.get_dataset_images_dir(dataset_name)
img_list = os.listdir(img_dir)
#%%
import random
random.seed()
#%%
test_txt_path = os.path.join(ultralytics_utils.get_dataset_dir(dataset_name), 'autosplit_test.txt')
with open(test_txt_path, 'r') as file:
    lines = file.readlines()
    test_case_ids = [line.strip()[9:] for line in lines]  # 去除每行末尾的换行符
#%%
import time
#%%
random_img = os.path.join(img_dir, random.choice(test_case_ids))
img = cv2.imread(random_img)
t1 = time.time()
model(img)
print(time.time() - t1)
result_img = model.draw_detections(img)
plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
plt.show()
#%%

#%%
