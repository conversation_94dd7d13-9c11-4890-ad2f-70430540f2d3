#%%
import pandas as pd
import sys
import os
import json
import cv2
import uuid
sys.path.append('../../../src')
#%%
from sryolo.devtools import os_utils
from sryolo.devtools import dataset_utils
from sryolo.devtools import label_studio_utils
from sryolo.devtools import ultralytics_utils
#%%
dataset_name = 'full-v1'
train_name = 'yolov8n-1088-full-v1'
save_name = 'yolov8n-1088-full-0428'
#%%
ultralytics_utils.export_model(dataset_name,
                               train_name=train_name,
                               save_name=save_name,
                              imgsz=(544, 960))
#%%
from ultralytics import YOLO
#%%
onnx_model = YOLO(os.path.join(os_utils.get_work_dir(), 'models', save_name, 'model.onnx'), task='detect')
#%%
val_result = onnx_model.val(
    data=ultralytics_utils.get_dataset_yaml_path(dataset_name),
    imgsz=1088
)
#%%
