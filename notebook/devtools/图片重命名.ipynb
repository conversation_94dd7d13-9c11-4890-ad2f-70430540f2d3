#%%
import pandas as pd
import sys
import os
import json
import cv2
import uuid
sys.path.append('../../src')
#%%
from sryolo.devtools import os_utils
from sryolo.devtools import dataset_utils
from sryolo.devtools import label_studio_utils
from sryolo.devtools import ultralytics_utils
#%%
label_studio_utils.rename_raw_images()
#%%
label_studio_utils.generate_tasks_by_predictions(
    dataset_name='full-2176',
    train_name='yolov8x-960-full',
    use_label='v1'
)
#%%
