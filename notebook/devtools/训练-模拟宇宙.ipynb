#%%
import pandas as pd
import sys
import os
sys.path.append('../../src')
#%%
from sryolo.devtools import dataset_utils
from sryolo.devtools import label_studio_utils
from sryolo.devtools import ultralytics_utils
from sryolo.utils import label_utils
#%%
ultralytics_utils.init_ultralytics_settings()
#%%
origin_img_size = 32 * 68
img_size_div = 2
#origin_img_size = 32 * 69
#img_size_div = 3
train_img_size = origin_img_size // img_size_div
export_img_size = (1080 // img_size_div, 1920 // img_size_div)

task_name = 'simuni'
dataset_name = f'{task_name}-{origin_img_size}'
objects_to_use = label_utils.read_sim_uni_objects()
labels_version = 'v1'

pretrained_model_name = 'yolov8n'
train_name = f'{pretrained_model_name}-{export_img_size[1]}-{task_name}'
#%%
dataset_name, train_name
#%%
objects_to_use
#%%
dataset_utils.prepare_dateset(dataset_name, objects_to_use=objects_to_use)
#%%
from ultralytics import YOLO
#%%
model = YOLO(ultralytics_utils.get_base_model_path(f'{pretrained_model_name}.pt'))
#%%
model.train(
    data=ultralytics_utils.get_dataset_yaml_path(dataset_name),  # 数据集配置文件的位置
    project=ultralytics_utils.get_dataset_model_dir(dataset_name),  # 训练模型的数据（包括模型文件）的自动保存位置
    name= train_name,
    imgsz=train_img_size,
    epochs=1000, 
    batch=-1,  # 根据可使用内存 自动判断batch_size
    exist_ok=True,
    copy_paste=1,  # 数据集太小，复制多一点
)
#%%
val_result = model.val(imgsz=train_img_size)
#%%
# model = YOLO(ultralytics_utils.get_train_model_path(dataset_name, train_name))
#%%
ultralytics_utils.export_model(dataset_name,
                               train_name=train_name,
                               imgsz=export_img_size)
#%%
