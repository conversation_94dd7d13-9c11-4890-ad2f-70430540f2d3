#%%
import pandas as pd
import sys
sys.path.append('../../src')
#%%
from sryolo.utils import label_utils
#%%
df = label_utils.read_label_csv()
#%%
cate_to_use = ['普通怪', '可破坏物', '模拟宇宙下层入口', '模拟宇宙下层入口未激活', '模拟宇宙事件', '模拟宇宙黑塔', '模拟宇宙沉浸奖励', '界面提示被锁定', '界面提示可攻击']
#%%
names = []
#%%
for index, row in df.iterrows():
    if row['cate'] not in cate_to_use:
        continue
    names.append(row['name'])
#%%
names
#%%
