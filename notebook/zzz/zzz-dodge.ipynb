#%%
import pandas as pd
import sys
import os
sys.path.append('../../src')
#%%
from one_dragon.yolo.devtools import os_utils
from one_dragon.yolo.devtools import ultralytics_utils
#%%
ultralytics_utils.init_ultralytics_settings()
#%%
from ultralytics import YOLO
#%%
model = YOLO(ultralytics_utils.get_base_model_path('yolov8n-cls.pt'))
#%%
dataset_name = 'zzz-dodge-raw'
#%%
imgsz=(540, 960)
#%%
model.train(
    data=ultralytics_utils.get_dataset_dir(dataset_name),  # 数据集配置文件的位置
    project=ultralytics_utils.get_dataset_model_dir(dataset_name),  # 训练模型的数据（包括模型文件）的自动保存位置
    epochs=200,
    imgsz=imgsz,
    batch=-1,  # 根据可使用内存 自动判断batch_size
    auto_augment=None,  # 关闭旋转, 倾斜等变换。闪避的红黄光是很正的十字，避免跟其他的闪关混淆
)
#%%
model.export(format="onnx", imgsz=640)
#%%
val_dir = os.path.join(ultralytics_utils.get_dataset_dir(dataset_name), 'val')
#%%
import random
#%%
class_dir = os.path.join(
    val_dir,
    random.choice(os.listdir(val_dir))
)
image = os.path.join(
    class_dir,
    random.choice(os.listdir(class_dir))
)
results = model(image)
#%%
from zzz_od.yolo.switch_classifier import SwitchClassifier
#%%
SwitchClassifier