#%%
import pandas as pd
import sys
import os
sys.path.append('../../src')
#%%
from one_dragon_yolo.devtools import os_utils
from one_dragon_yolo.devtools import ultralytics_utils
#%%
ultralytics_utils.init_ultralytics_settings()
#%%
from one_dragon_yolo.zzz.flash_classify.yolo import DodgeYolo
#%%
model = DodgeYolo(ultralytics_utils.get_base_model_path('yolov8n-cls.pt'))
#%%
dataset_name = 'zzz_dodge_raw'
train_name = 'train'
export_img_size = 640
model_name = 'best'
#%%
model.train(
    data=ultralytics_utils.get_dataset_dir(dataset_name),  # 数据集配置文件的位置
    project=ultralytics_utils.get_dataset_model_dir(dataset_name),  # 训练模型的数据（包括模型文件）的自动保存位置
    epochs=200,
    imgsz=640,
    batch=-1,  # 根据可使用内存 自动判断batch_size
    scale=1,  # 部分图片需要缩放处理
    flipud=0, fliplr=0,  # 不需要翻转对称
    erasing=0,  # 不需要消除了 图片下方空白比较多
    hsv_h=0, hsv_s=0, hsv_v=0,  # 关闭色彩调节
)
#%%
ultralytics_utils.export_cls_model(
    dataset_name=dataset_name,
    train_name=train_name,
    imgsz=export_img_size,
    model_name=model_name,
    save_name=f'{train_name}-{model_name}'
)
#%%
val_dir = os.path.join(ultralytics_utils.get_dataset_dir(dataset_name), 'val')
#%%
import random
#%%
class_dir = os.path.join(
    val_dir,
    random.choice(os.listdir(val_dir))
)
image = os.path.join(
    class_dir,
    random.choice(os.listdir(class_dir))
)
results = model(image)
#%%
