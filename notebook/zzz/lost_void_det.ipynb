#%%
import pandas as pd
import sys
import os
import math
sys.path.append('../../src')
#%%
from one_dragon_yolo.devtools import os_utils
from one_dragon_yolo.devtools import ultralytics_utils
from one_dragon_yolo.devtools import yolo_dataset_utils
#%%
ultralytics_utils.init_ultralytics_settings()
#%%
# origin_img_size = 32 * 68
# img_size_div = 2
origin_img_size = 32 * 69
img_size_div = 3
train_img_size = origin_img_size // img_size_div

export_width = origin_img_size // img_size_div
export_height = math.ceil((export_width // 16 * 9) * 1.0 / 32) * 32
export_img_size = (export_height, export_width) # 由于训练时候没有开启缩放，使用训练的尺寸效果会更好

dataset_name = f'zzz_lost_void_det_{origin_img_size}'

# pretrained_model_name = 'yolo11n'
pretrained_model_name = 'yolov8n'
train_name = f'{pretrained_model_name}-{train_img_size}'
#%%
dataset_name, train_name, export_img_size
#%%
yolo_dataset_utils.init_dataset(
    dataset_name=dataset_name,
    raw_dataset_name='zzz_lost_void_det_raw',
    raw_images_dir_path=os_utils.get_path_under_work_dir('label_studio', 'zzz', 'lost_void_det', 'raw'),
    target_img_size=origin_img_size,
    split_weights=(1, 0, 0)
)
#%%
from ultralytics import YOLO
#%%
model = YOLO(ultralytics_utils.get_base_model_path(f'{pretrained_model_name}.pt'))
#%%
model.train(
    data=ultralytics_utils.get_dataset_yaml_path(dataset_name),  # 数据集配置文件的位置
    project=ultralytics_utils.get_dataset_model_dir(dataset_name),  # 训练模型的数据（包括模型文件）的自动保存位置
    name= train_name,
    imgsz=train_img_size,
    epochs=1000,
    save_period=100,
    batch=30,  # 根据可使用内存 自动判断batch_size
    val=False,  # 关闭验证
    exist_ok=True,
    scale=0,  # 不需要缩放处理
    flipud=0, fliplr=0,  # 不需要翻转对称
    erasing=0,  # 不需要消除了 图片下方空白比较多
    hsv_h=0, hsv_s=0, hsv_v=0,  # 关闭色彩调节
    mosaic=0,  # 不需要拼接 使用原装大小
)
#%%
ultralytics_utils.export_model(
    dataset_name=dataset_name,
    train_name=train_name,
    imgsz=export_img_size
)
#%%
for i in range(1, 10):
    model_name = 'epoch%d00' % i
    ultralytics_utils.export_model(
        dataset_name=dataset_name,
        train_name=train_name,
        imgsz=export_img_size,
        model_name=model_name,
        save_name=f'{train_name}-{model_name}'
    )
#%%
